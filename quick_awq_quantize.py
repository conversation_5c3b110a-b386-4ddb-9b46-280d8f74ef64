#!/usr/bin/env python3
"""
快速AWQ量化脚本
将 /opt/models/Seed-Coder-8B-Merged 量化为 /opt/models/Seed-Coder-8B-Merged-awq
"""

import os
import torch
import gc
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

try:
    from awq import AutoAWQForCausalLM
    from transformers import AutoTokenizer
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装 autoawq 库: pip install autoawq")
    exit(1)

def main():
    print("🚀 开始快速AWQ量化...")
    
    # 路径配置
    input_path = "/opt/models/Seed-Coder-8B-Merged"
    output_path = "/opt/models/Seed-Coder-8B-Merged-awq"
    
    print(f"输入: {input_path}")
    print(f"输出: {output_path}")
    
    # 检查输入路径
    if not os.path.exists(input_path):
        print(f"❌ 错误: 输入模型路径不存在 {input_path}")
        return
    
    # 创建输出目录
    os.makedirs(output_path, exist_ok=True)
    
    try:
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()
        
        print("📝 加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(input_path, trust_remote_code=True)
        
        print("🔧 加载模型...")
        model = AutoAWQForCausalLM.from_pretrained(
            input_path,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16
        )
        
        print("⚙️ 配置量化参数...")
        quant_config = {
            "zero_point": True,
            "q_group_size": 128,
            "w_bit": 4,
            "version": "GEMM"
        }
        
        print("🔄 开始量化（请耐心等待）...")
        model.quantize(
            tokenizer,
            quant_config=quant_config,
            calib_data="pileval",
            split="train[:512]",  # 使用512个样本
            text_column="text",
            duo_scaling=True,
            export_compatible=True,
            apply_clip=True,
            n_parallel_calib_samples=1,
            max_calib_samples=256,
            max_calib_seq_len=1024
        )
        
        print("💾 保存量化模型...")
        model.save_quantized(output_path)
        tokenizer.save_pretrained(output_path)
        
        print("✅ 量化完成！")
        print(f"📁 量化模型保存在: {output_path}")
        
        # 简单验证
        print("🔍 验证模型...")
        del model
        torch.cuda.empty_cache()
        
        # 重新加载验证
        test_model = AutoAWQForCausalLM.from_quantized(
            output_path,
            device_map="auto",
            trust_remote_code=True
        )
        print("✅ 模型验证成功！")
        
    except Exception as e:
        print(f"❌ 量化失败: {str(e)}")
        print("请检查CUDA内存是否足够，或尝试减少batch size")
        return

if __name__ == "__main__":
    main()
