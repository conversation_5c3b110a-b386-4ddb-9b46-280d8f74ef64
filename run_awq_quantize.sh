#!/bin/bash

echo "🚀 启动AWQ量化流程..."

# 设置环境变量优化内存使用
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export CUDA_LAUNCH_BLOCKING=0

# 激活conda环境
source /root/miniforge3/etc/profile.d/conda.sh
conda activate edit-pred-model

# 清理GPU缓存
echo "🧹 清理GPU缓存..."
python -c "import torch; torch.cuda.empty_cache() if torch.cuda.is_available() else None"

# 检查输入模型是否存在
if [ ! -d "/opt/models/Seed-Coder-8B-Merged" ]; then
    echo "❌ 错误: 输入模型不存在 /opt/models/Seed-Coder-8B-Merged"
    echo "请先运行 merge_lora.py 生成合并后的模型"
    exit 1
fi

echo "📊 系统信息:"
echo "   - GPU: $(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)"
echo "   - CUDA版本: $(nvcc --version | grep release | awk '{print $6}' | cut -c2-)"
echo "   - PyTorch版本: $(python -c 'import torch; print(torch.__version__)')"
echo ""

# 运行量化
echo "🔄 开始AWQ量化..."
python quick_awq_quantize.py

# 检查结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 AWQ量化成功完成！"
    echo "📁 量化后的模型位置: /opt/models/Seed-Coder-8B-Merged-awq"
    echo ""
    echo "📊 模型大小对比:"
    echo "原始模型: $(du -sh /opt/models/Seed-Coder-8B-Merged 2>/dev/null | cut -f1 || echo '未知')"
    echo "AWQ模型:  $(du -sh /opt/models/Seed-Coder-8B-Merged-awq 2>/dev/null | cut -f1 || echo '未知')"
    echo ""
    echo "🚀 现在可以使用AWQ量化模型进行推理了！"
else
    echo ""
    echo "❌ AWQ量化失败"
    echo "请检查错误信息并重试"
    exit 1
fi
