import os
import multiprocessing
from vllm import LLM,SamplingParams
import Levenshtein
import sacrebleu
import json

model_path = "/opt/models/Seed-Coder-8B-Merged"

# Try different approaches to fix CUDA multiprocessing issue
# Option 1: Reduce tensor parallelism to 1 (most reliable)
llm = LLM(model_path, tensor_parallel_size=1, max_model_len=20000)

# Option 2: If you need tensor_parallel_size=2, try with specific GPU settings
# llm = LLM(model_path, tensor_parallel_size=2, gpu_memory_utilization=0.8)
def make_prompt(input, events):
    prompt = f'''### Instruction:
You are a code completion assistant and your task is to analyze user edits and then rewrite an excerpt that the user provides, suggesting the appropriate edits within the excerpt, taking into account the cursor location.
### User Edits:

{events}

### User Excerpt:

{input}

### Response:

'''
    return prompt

def read_jsonl(file_path):
    """
    从JSONL文件中读取数据，并根据特定标记截取prompt和label。
    """
    prompts = []
    labels = []

    start_marker = "<|editable_region_start|>"
    end_marker = "<|editable_region_end|>"

    with open(file_path, 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            try:
                data_line = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行跳过无效JSON: {e}")
                continue
            required_keys = ['input', 'output', 'events']
            if not all(key in data_line for key in required_keys):
                print(f"第 {line_num} 行跳过缺失字段的行（需要包含{required_keys}）")
                continue

            full_output_for_label = data_line['output']

            start_index = full_output_for_label.find(start_marker)
            if start_index == -1:
                print(f"警告: 第 {line_num} 行的label中未找到start_marker，已跳过。")
                continue

            focused_region = full_output_for_label[start_index:]

            end_index = focused_region.find(end_marker)
            if end_index == -1:
                print(f"警告: 第 {line_num} 行的label中未找到 '{end_marker}'，已跳过。")
                continue
            
            final_label = focused_region[:end_index + len(end_marker)]

            prompts.append(make_prompt(data_line['input'], data_line['events'])) #
            labels.append(final_label)

    return prompts, labels


datas, labels = read_jsonl("/root/yzn/edit-pred-model/datasets/eval.jsonl")
bleu_sum_score,Levenshtein_sum_score,num = 0.0,0.0,0.0
for prompt,label in zip(datas, labels):
    sampling_params = SamplingParams(
        temperature=0, 
        max_tokens=2048,
        skip_special_tokens=False,
    )
    outputs = llm.generate([prompt], sampling_params)
    predicted_text = outputs[0].outputs[0].text
    # 由于这里其它模型都是以mark结尾作为标记，公平起见，zeta也进行截取
    if model_path == "/data/jiangh/nju-devops/LLM/zeta/":
        original_end_marker = "<|editable_region_end|>"
        output_end_index = predicted_text.find(original_end_marker)
        predicted_text = predicted_text[:output_end_index + len(original_end_marker)]
    # print(f'预测的文本:{predicted_text}')
    bleu_score = sacrebleu.corpus_bleu([predicted_text],[[label]])
    bleu_sum_score += bleu_score.score
    Levenshtein_score = Levenshtein.ratio(predicted_text, label) * 100
    Levenshtein_sum_score += Levenshtein_score
    num += 1
    print(f'第{num}次的BLEU分数: {bleu_score.score:.4f}')  
    print(f'第{num}次的编辑相似度: {Levenshtein_score:.4f}') 
print(f'平均BLEU分数: {bleu_sum_score/num}')
print(f'平均编辑相似度: {Levenshtein_sum_score/num}')